import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { BbTaskParams } from "api";
import {
  ALLOW_REPORT_STATUS_MAP,
  DANGER_MANAGE_TYPE_MAP,
  DANGER_REPORT_STATUS_MAP,
  DANGER_SOURCE_MAP,
  DANGER_STATUS_MAP,
  OVERTIME_STATUS_MAP,
  SNAP_DANGER_CATEGORY_MAP,
  SNAP_DANGER_TYPE_MAP,
  SNAP_LEVEL_MAP,
  SNAP_SOURCE_MAP,
} from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const dangerConfigModalAtom = atom(false);

export interface Danger {
  /**
   * 验收意见
   */
  acceptComment: string;
  /**
   * 验收人
   */
  acceptor: Acceptor;
  /**
   * 验收人候选列表
   */
  acceptorCands: AcceptorCand[];
  /**
   * 验收图片
   */
  acceptPictures: string;
  acceptResult?: number;
  /**
   * 验收时间
   */
  acceptTime: string;
  /**
   * 整改措施
   */
  controlMessure: string;
  cost: string;
  /**
   * 隐患类别
   */
  dangerCategory: number;
  /**
   * 隐患类型
   */
  dangerType: number;
  /**
   * 治理期限
   */
  deadline?: Date;
  /**
   * 隐患描述
   */
  description: string;
  /**
   * 评估意见
   */
  evaluateComment: string;
  evaluateResult: EvaluateResult;
  /**
   * 评估时间
   */
  evaluateTime: string;
  /**
   * 评估人
   */
  evaluator: Evaluator;
  /**
   * 评估人候选人列表
   */
  evalatorCands: EvaluatorCand[];
  /**
   * 隐患记录
   */
  id: number;
  /**
   * 隐患等级
   */
  level: number;
  /**
   * 治理类型
   */
  manageType: ManageType;
  /**
   * 隐患名称
   */
  name?: string;
  /**
   * 原因分析
   */
  reason: string;
  /**
   * 整改人
   */
  rectifier: Rectifier;
  /**
   * 整改人候选列表
   */
  rectifierCands: RectifierCand[];
  /**
   * 整改图片
   */
  rectifyPictures: string;
  /**
   * 整改时间
   */
  rectifyTime: string;
  /**
   * 上报人列表
   */
  reporters?: Reporter[];
  /**
   * 隐患图片
   */
  reportPictures: string;
  /**
   * 上报时间
   */
  reportTime?: Date;
  /**
   * 风险对象
   */
  riskObject?: RiskObject;
  /**
   * 隐患来源
   */
  source: number;
  /**
   * 隐患状态
   */
  status: DangerStatus;
  [property: string]: any;
}

export enum EvaluateResult {
  None,
  Normal,
  Evaluate,
}

export enum DelayApproveResult {
  None,
  Approved,
  Rejected,
}

/**
 * 风险对象
 */
export interface RiskObject {
  id: number;
  name: string;
  [property: string]: any;
}

/**
 * 验收人
 */
export interface Acceptor {
  id: number;
  name: string;
  [property: string]: any;
}

export interface AcceptorCand {
  id: number;
  name: string;
  [property: string]: any;
}

/**
 * 评估人
 */
export interface Evaluator {
  id: number;
  name: string;
  [property: string]: any;
}

export interface EvaluatorCand {
  id: number;
  name: string;
  [property: string]: any;
}

/**
 * 整改人
 */
export interface Rectifier {
  id: number;
  name: string;
  [property: string]: any;
}

export interface RectifierCand {
  id: number;
  name: string;
  [property: string]: any;
}

export interface Reporter {
  id: number;
  name: string;
  [property: string]: any;
}

export enum DangerModalType {
  Detail,
  // 1 即查即改
  Immediately,
  // 2 限期整改
  Delayed,
}

export enum ManageType {
  None,
  // 1 即查即改
  Immediately,
  // 2 限期整改
  Delayed,
}

export enum DangerSteps {
  /* 上报 */
  Report,
  /* 评估 */
  Evaluate,
  // 整改
  Rectify,
  // 验收
  Accept,
}

export enum DangerStatus {
  /* 已上报 */
  Reported,
  /* 待评估 */
  WaitEvaluate,
  /* 无需整改 */
  NoRectify,
  /* 待整改 */
  WaitRectify,
  /* 待验收 */
  WaitAccept,
  /* 验收通过 */
  Accepted,
  /* 验收不通过 */
  NotAccepted,
  /* 申请延期 */
  RequestDelay,
}

export const dangerEditModalAtom = atomWithReset<{
  record?: Danger;
  show: boolean;
}>({
  record: undefined,
  show: false,
});

// 查询条件
export const dangerFilterAtom = atomWithReset<BbTaskParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const dangerFnAtom = atom({
  refetch: () => {},
});

export const dangerColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
    fixed: true,
  },
  {
    title: <Tooltip content="隐患名称">隐患名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    width: 150,
    fixed: true,
  },
  {
    title: <Tooltip content="风险对象名称">风险对象名称</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="上报时间">上报时间</Tooltip>,
    dataIndex: "reportTime",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{dayjs(r).format("YYYY-MM-DD HH:mm")}</p>,
  },
  {
    title: <Tooltip content="上报人列表">上报人列表</Tooltip>,
    dataIndex: "reporters",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item || !item?.length) {
        return null;
      }
      return (
        <Tooltip content={item.map((o) => o.name).toString()}>
          <div className="flex gap-1">
            {(item ?? []).map((o) => (
              <Tag color="grey" type="light" className="min-w-[fit-content]">
                {o.name}
              </Tag>
            ))}
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: "录音备注",
    dataIndex: "soundNote",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const url = base_url + item;
      return (
        <Tooltip content={item.split("/").pop()}>
          <a href={url} target="_blank">
            {" "}
            {item.split("/").pop()}
          </a>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="隐患来源">隐患来源</Tooltip>,
    dataIndex: "source",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(SNAP_SOURCE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="隐患类型">隐患类型</Tooltip>,
    dataIndex: "dangerType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(SNAP_DANGER_TYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="隐患类别">隐患类别</Tooltip>,
    dataIndex: "dangerCategory",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(SNAP_DANGER_CATEGORY_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="治理类型">治理类型</Tooltip>,
    dataIndex: "manageType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_MANAGE_TYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="隐患等级">隐患等级</Tooltip>,
    dataIndex: "level",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => {
      const i = find(propEq(item, "id"))(SNAP_LEVEL_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="隐患状态">隐患状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="是否上报">是否上报</Tooltip>,
    dataIndex: "allowUpload",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="是否逾期">是否逾期</Tooltip>,
    dataIndex: "isOvertime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(OVERTIME_STATUS_MAP);
      if (!i) {
        return null;
      }
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: "隐患出处",
    dataIndex: "internalSource",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(DANGER_SOURCE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  // {
  //   title: <Tooltip content="评估结果">评估结果</Tooltip>,
  //   dataIndex: 'evaluateResult',
  //   isShow: true,
  //   ellipsis: true,
  //   render: (item) => {
  //     const i = find(propEq(item ? item : 1, 'id'))(DANGER_EVALUATE_RESULT_MAP)
  //     return (
  //       <Tag color={i.color} type="light">
  //         {i.name}
  //       </Tag>
  //     )
  //   }
  // }, {
  //   title: <Tooltip content="评估候选人列表">评估候选人列表</Tooltip>,
  //   dataIndex: 'evalatorCands',
  //   isShow: true,
  //   ellipsis: true,
  //   render: (item) => {
  //     if (!item || !item?.length) {
  //       return null
  //     }
  //     return (
  //       <Tooltip content={item.map(o => o.name).toString()}>
  //         <div className="flex gap-1">

  //           {
  //             (item ?? []).map(o => (
  //               <Tag color="grey" type="light" className="min-w-[fit-content]">
  //                 {o.name}
  //               </Tag>
  //             ))
  //           }
  //         </div>
  //       </Tooltip>
  //     )
  //   }
  // }, {
  //   title: <Tooltip content="整改候选人列表">整改候选人列表</Tooltip>,
  //   dataIndex: 'rectifierCands',
  //   isShow: true,
  //   ellipsis: true,
  //   render: (item) => {
  //     if (!item || !item?.length) {
  //       return null
  //     }
  //     return (
  //       <Tooltip content={item.map(o => o.name).toString()}>
  //         <div className="flex gap-1">

  //           {
  //             (item ?? []).map(o => (
  //               <Tag color="grey" type="light" className="min-w-[fit-content]">
  //                 {o.name}
  //               </Tag>
  //             ))
  //           }
  //         </div>
  //       </Tooltip>
  //     )
  //   }
  // }, {
  //   title: <Tooltip content="验收候选人列表">验收候选人列表</Tooltip>,
  //   dataIndex: 'acceptorCands',
  //   isShow: true,
  //   ellipsis: true,
  //   render: (item) => {
  //     if (!item || !item?.length) {
  //       return null
  //     }
  //     return (
  //       <Tooltip content={item.map(o => o.name).toString()}>
  //         <div className="flex gap-1">

  //           {
  //             (item ?? []).map(o => (
  //               <Tag color="grey" type="light" className="min-w-[fit-content]">
  //                 {o.name}
  //               </Tag>
  //             ))
  //           }
  //         </div>
  //       </Tooltip>
  //     )
  //   }
  // },
]);
