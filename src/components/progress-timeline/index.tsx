import React, { useState } from "react";

import { IconAlertTriangle, IconCheckboxTick } from "@douyinfe/semi-icons";
import { Spin, Timeline } from "@douyinfe/semi-ui";
import { renderEmployeeSimpleNames } from "components";
import { formatDate } from "utils";

import FinishListDisplay from "./FinishListDisplay";
import ImageGalleryModal from "./ImageGalleryModal";
import {
  ProgressItem,
  ProgressTimelineProps,
  SubprocessItem,
  TimelineStatusResult,
} from "./types";

// Export all types for external use
export * from "./types";

// Export sub-components for external use
export { default as FinishListDisplay } from "./FinishListDisplay";
export { default as ImageGalleryModal } from "./ImageGalleryModal";

/**
 * Renders status icon and type based on status number
 * @param status - Status number (1: error, other: success)
 * @returns Timeline status configuration
 */
const renderStatus = (status: number): TimelineStatusResult => {
  if (status === 1) {
    return {
      type: "error",
      dot: <IconAlertTriangle />,
    };
  }
  return {
    type: "success",
    dot: <IconCheckboxTick />,
  };
};

/**
 * Renders the content for each progress item including subprocess timeline
 */
const CardContent: React.FC<{ item: ProgressItem }> = ({ item }) => {
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [currentImageList, setCurrentImageList] = useState<string[]>([]);

  /**
   * Handles image click event from FinishListDisplay
   * Opens the image gallery modal with the provided image list
   * @param imageList - Array of image URLs to display
   */
  const handleImageClick = (imageList: string[]) => {
    if (imageList && imageList.length > 0) {
      setCurrentImageList(imageList);
      setImageModalVisible(true);
    }
  };

  return (
    <div className="flex flex-col">
      <h2>{item.name}</h2>
      <Timeline mode="left">
        {(item?.subprocessList ?? []).map(
          (subprocess: SubprocessItem, index: number) => (
            <Timeline.Item
              time={
                subprocess.status === 1
                  ? ""
                  : `完成时间: ${formatDate(subprocess.finishTime as any) || "未定义"}`
              }
              {...renderStatus(subprocess.status)}
              extra={
                <>
                  {subprocess.status === 1 ? (
                    <p className="text-red-400">
                      未完成, 等待
                      {renderEmployeeSimpleNames(
                        subprocess.candidateList || []
                      ).join(",")}
                      &nbsp;处理
                    </p>
                  ) : null}
                  {/* Display finishList when available */}
                  {subprocess.finishList &&
                    subprocess.finishList.length > 0 && (
                      <FinishListDisplay
                        finishList={subprocess.finishList}
                        onImageClick={handleImageClick}
                      />
                    )}
                </>
              }
              key={index}
            >
              {subprocess.name}
            </Timeline.Item>
          )
        )}
      </Timeline>

      {/* Image Gallery Modal */}
      <ImageGalleryModal
        visible={imageModalVisible}
        imageList={currentImageList}
        onClose={() => setImageModalVisible(false)}
      />
    </div>
  );
};

/**
 * ProgressTimeline component for displaying progress timeline with subprocesses
 * @param props - Component props containing data and loading state
 * @returns JSX element for progress timeline
 */
export const ProgressTimeline: React.FC<ProgressTimelineProps> = ({
  data,
  isLoading = false,
}) => {
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex w-full justify-center items-center py-6">
        <Spin size="large" />
      </div>
    );
  }

  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <div className="flex w-full justify-center items-center py-6">
        <div className="text-gray-500">暂无进度数据</div>
      </div>
    );
  }

  return (
    <div className="flex w-full justify-center items-center py-6">
      <div className="w-1/3">
        <Timeline
          mode="left"
          dataSource={data.map((item: ProgressItem) => ({
            content: <CardContent item={item} />,
            ...renderStatus(item.status),
          }))}
        />
      </div>
    </div>
  );
};

export default ProgressTimeline;
