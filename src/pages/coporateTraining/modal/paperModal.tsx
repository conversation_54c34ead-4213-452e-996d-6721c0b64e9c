import { IconMinusCircle, IconPlusCircle } from "@douyinfe/semi-icons";
import {
  <PERSON>rrayField,
  Banner,
  Button,
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getQuestionList, paperApis } from "api/coporateTraining";
import { paperAtoms, questionFilterAtom, questionShowColumnsAtom } from "atoms";
import { CT_PAPER_GENERATETYPE_MAP, Upload } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { TrainingSubjectSearch } from "components/search/trainingSubjectSearch";
import QuestionPickerTable from "components/table/questionPickerTable";
import { questionPickDataKeys } from "components/table/questionTableModal";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { filterEditData, trainingEditTooltip } from "utils";

const requiredRule = { required: true, message: "此为必填项" };
const rules = [requiredRule];
const rowStyle = {
  marginTop: 12,
  marginLeft: 12,
};

const FormDebugComponentUsingFormState = () => {
  const formState = useFormState();
  return (
    <>
      <TextArea rows={8} value={JSON.stringify(formState.values, null, 2)} />
    </>
  );
};
const RandomQuestionComponentUsingFormApi = () => {
  const formApi = useFormApi();
  const formState = useFormState();
  const [totalScore, setTotalScore] = useState(0);
  const [calculatedScore, setCalculatedScore] = useState(0);

  // 计算随机规则的总分
  useEffect(() => {
    const randomRuleList = formState.values?.randomRuleList || [];
    if (randomRuleList.length > 0) {
      const calculatedTotal = randomRuleList.reduce((total, rule) => {
        // 确保即使输入不完整也能计算
        const questionNum = Number(rule.questionNum) || 0;
        const score = Number(rule.score) || 0;
        return total + questionNum * score;
      }, 0);
      setCalculatedScore(calculatedTotal);
    } else {
      setCalculatedScore(0);
    }

    const currentTotalScore = formState.values?.totalScore;
    if (currentTotalScore) {
      setTotalScore(Number(currentTotalScore));
    }
  }, [formState.values, formState.values?.randomRuleList]);

  return (
    <div className="relative">
      <div className="absolute right-0 top-[-10px]">
        <span className="text-gray-500 mr-4">
          总计分值:{" "}
          <span
            className={`font-medium ${calculatedScore === totalScore ? "text-green-500" : "text-red-500"}`}
          >
            {calculatedScore}分
            {calculatedScore !== totalScore &&
              ` (与试卷总分${totalScore}不一致)`}
          </span>
        </span>
      </div>
      <ArrayField field="randomRuleList">
        {({ add, arrayFields, addWithInitValue }) => (
          <>
            <Button
              onClick={() => {
                add();
                // 强制触发一次重新计算
                setTimeout(() => {
                  const currentRules = formApi.getValue("randomRuleList") || [];
                  const newTotal = currentRules.reduce((total, rule) => {
                    const questionNum = Number(rule.questionNum) || 0;
                    const score = Number(rule.score) || 0;
                    return total + questionNum * score;
                  }, 0);
                  setCalculatedScore(newTotal);
                }, 0);
              }}
              icon={<IconPlusCircle />}
              theme="light"
            >
              新增
            </Button>
            {arrayFields.map(({ field, key, remove }, i) => (
              <div key={key} style={{ width: 700, display: "flex" }}>
                <TrainingSubjectSearch
                  label="知识科目"
                  field={`${field}.subjectId`}
                  placeholder=""
                  isRequired
                />
                <Form.InputNumber
                  label="题目数量"
                  field={`${field}.questionNum`}
                  rules={rules}
                  className="w-16"
                  onChange={(value) => {
                    // 当题目数量变化时，重新计算总分
                    setTimeout(() => {
                      const currentRules =
                        formApi.getValue("randomRuleList") || [];
                      const newTotal = currentRules.reduce((total, rule) => {
                        const questionNum = Number(rule.questionNum) || 0;
                        const score = Number(rule.score) || 0;
                        return total + questionNum * score;
                      }, 0);
                      setCalculatedScore(newTotal);
                    }, 0);
                  }}
                />
                <Form.InputNumber
                  label="每题分值"
                  field={`${field}.score`}
                  rules={rules}
                  className="w-24"
                  suffix="分"
                  onChange={(value) => {
                    // 当分值变化时，重新计算总分
                    setTimeout(() => {
                      const currentRules =
                        formApi.getValue("randomRuleList") || [];
                      const newTotal = currentRules.reduce((total, rule) => {
                        const questionNum = Number(rule.questionNum) || 0;
                        const score = Number(rule.score) || 0;
                        return total + questionNum * score;
                      }, 0);
                      setCalculatedScore(newTotal);
                    }, 0);
                  }}
                />
                <Button
                  type="danger"
                  theme="borderless"
                  icon={<IconMinusCircle />}
                  onClick={() => {
                    remove();
                    // 删除后重新计算总分
                    setTimeout(() => {
                      const currentRules =
                        formApi.getValue("randomRuleList") || [];
                      const newTotal = currentRules.reduce((total, rule) => {
                        const questionNum = Number(rule.questionNum) || 0;
                        const score = Number(rule.score) || 0;
                        return total + questionNum * score;
                      }, 0);
                      setCalculatedScore(newTotal);
                    }, 0);
                  }}
                  style={rowStyle}
                />
                <Button
                  icon={<IconPlusCircle />}
                  style={rowStyle}
                  disabled={i !== arrayFields.length - 1}
                  onClick={() => {
                    addWithInitValue({});
                    // 添加后重新计算总分
                    setTimeout(() => {
                      const currentRules =
                        formApi.getValue("randomRuleList") || [];
                      const newTotal = currentRules.reduce((total, rule) => {
                        const questionNum = Number(rule.questionNum) || 0;
                        const score = Number(rule.score) || 0;
                        return total + questionNum * score;
                      }, 0);
                      setCalculatedScore(newTotal);
                    }, 0);
                  }}
                />
              </div>
            ))}
          </>
        )}
      </ArrayField>
    </div>
  );
};

export const PaperModal = () => {
  const operation = "Edit";
  const entityCname = "试卷信息";
  const newTitle = "新增" + entityCname; //user-defined code here
  const editTitle = "编辑" + entityCname; //user-defined code here

  const gutter = 24;

  const atoms = paperAtoms;
  const apis = paperApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const [selectedRecord, setSelectedRecord] = useAtom(questionPickDataKeys);
  const [keys, setKeys] = useAtom(questionPickDataKeys);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      if (items.randomRuleList) {
        items.randomRuleList = items.randomRuleList.map((item, index) => {
          return {
            ...item,
            subjectId: item.subject.id,
          };
        });
      }
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        // 验证总分与题目分数总和是否一致
        if (values.generationMode === CT_PAPER_GENERATETYPE_MAP[0].id) {
          // 手动选题模式
          const questionList = values.questionList || [];
          if (questionList.length > 0) {
            const questionTotalScore = questionList.reduce(
              (total, question) => {
                return total + (question.score || 0);
              },
              0
            );

            if (questionTotalScore !== values.totalScore) {
              Toast.error(
                `试卷总分(${values.totalScore})与题目总分(${questionTotalScore})不一致，请检查`
              );
              return;
            }
          }
        } else if (values.generationMode === CT_PAPER_GENERATETYPE_MAP[1].id) {
          // 随机抽题模式
          const randomRuleList = values.randomRuleList || [];
          if (randomRuleList.length > 0) {
            const randomTotalScore = randomRuleList.reduce((total, rule) => {
              return total + (rule.questionNum * rule.score || 0);
            }, 0);

            if (randomTotalScore !== values.totalScore) {
              Toast.error(
                `试卷总分(${values.totalScore})与随机抽题总分(${randomTotalScore})不一致，请检查`
              );
              return;
            }
          }
        }

        let obj = {
          ...values,
          //user-defined code here
        };
        console.log(obj);

        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
        setSelectedRecord([]);
        setKeys([]);
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        {data?.data?.referedOngointPlanList.length > 0 ? (
          <Banner
            type="warning"
            description={trainingEditTooltip(
              "课件",
              data?.data?.referedOngointPlanList
            )}
          ></Banner>
        ) : null}
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {/* add form items here */}
          {({ formState }) => (
            <>
              {/* <FormDebugComponentUsingFormState /> */}
              <Form.Section text="基本信息">
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.Input
                      label="试卷名称"
                      field="name"
                      trigger="blur"
                      rules={rules}
                    />
                  </Col>
                  <Col span={12}>
                    <TrainingSubjectSearch
                      label="知识科目"
                      field="subjectId"
                      placeholder=""
                      isRequired
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.InputNumber
                      label="考试时长"
                      field="durationMinutes"
                      trigger="blur"
                      rules={rules}
                      suffix="分钟"
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.TextArea
                      label="试卷介绍"
                      field="note"
                      trigger="blur"
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={24}>
                    {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
                    <Upload
                      label="试卷封面"
                      formField="image"
                      field="image_upload"
                      multiple={false}
                      // arrayProcessType='array'
                      // type='file'
                      accept=".jpg,.png,.gif,.jpeg"
                      maxSize={51200} //KB
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.InputNumber
                      label="及格分"
                      field="passScore"
                      trigger="blur"
                      suffix="分"
                      onChange={(value) => {
                        // 当及格分变化时，重新验证总分字段
                        setTimeout(() => {
                          getFormApiRef.current?.validate(["totalScore"]);
                        }, 0);
                      }}
                      rules={[
                        requiredRule,
                        {
                          validator: (rule, value) =>
                            value &&
                            value >= 0 &&
                            value <= formState.values?.totalScore,
                          message: "及格分必须在0到试卷总分之间",
                        },
                        /* {
                          validator: (
                            rule: Rules,
                            value: any,
                            callback: (error?: string) => void
                          ) => {
                            if (value > formState.values?.totalScore) {
                              callback("及格分不能大于试卷总分");
                            } else {
                              callback();
                            }
                          },
                        }, */
                      ]}
                    />
                  </Col>
                  <Col span={12}>
                    <Form.InputNumber
                      label="试卷总分"
                      field="totalScore"
                      trigger="blur"
                      suffix="分"
                      onChange={(value) => {
                        // 当总分变化时，重新验证及格分字段
                        setTimeout(() => {
                          getFormApiRef.current?.validate(["passScore"]);
                        }, 0);
                      }}
                      rules={[
                        requiredRule,
                        {
                          validator: (rule, value) =>
                            value && value >= formState.values?.passScore,
                          message: "试卷总分不能小于及格分",
                        },
                        /* {
                          validator: (
                            rule: Rules,
                            value: any,
                            callback: (error?: string) => void
                          ) => {
                            if (value < formState.values?.passScore) {
                              callback("试卷总分不能小于及格分");
                            } else {
                              callback();
                            }
                          },
                        }, */
                      ]}
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.RadioGroup
                      field="generationMode"
                      label="出题方式"
                      type="button"
                      rules={[{ required: true, message: "此为必填项" }]}
                    >
                      {CT_PAPER_GENERATETYPE_MAP.map((item) => (
                        <Form.Radio key={item.id} value={item.id}>
                          {item.name}
                        </Form.Radio>
                      ))}
                    </Form.RadioGroup>
                  </Col>
                </Row>
              </Form.Section>
              {formState.values?.generationMode ===
              CT_PAPER_GENERATETYPE_MAP[0].id ? (
                <QuestionPickerTable
                  title="试题"
                  entity="Question"
                  entityTitle="试题"
                  field="questionIdList"
                  entityField="questionList"
                  list={data?.data?.questionList}
                  listApi={getQuestionList}
                  columnsAtom={questionShowColumnsAtom}
                  filterAtom={questionFilterAtom}
                  keyColumn="questionId"
                />
              ) : formState.values?.generationMode ===
                CT_PAPER_GENERATETYPE_MAP[1].id ? (
                <RandomQuestionComponentUsingFormApi />
              ) : null}
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
