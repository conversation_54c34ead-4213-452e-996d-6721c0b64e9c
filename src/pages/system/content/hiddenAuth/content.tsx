import {
  IconChevronLeft,
  IconChevronRight,
  IconSetting,
} from "@douyinfe/semi-icons";
import { Button, Form, Spin, Toast, Tooltip } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getThirdLoginSettings,
  setThirdLoginSettings,
} from "api/system/hidden";
import { AnimatePresence, motion } from "framer-motion";
import { omit } from "ramda";
import { useEffect, useMemo, useRef, useState } from "react";
import { filterEditData } from "utils";
import { AUTH_PLUGIN_META } from "./authPluginMeta";
import type { FormValues } from "./types";

export const HiddenAuthContent = ({ readonly = false, ...restProps }) => {
  const queryClient = useQueryClient();
  const queryKey = ["getThirdLoginSettings"];

  const formRef = useRef<any>(null);

  const { isLoading, data } = useQuery({
    queryKey: [queryKey],
    queryFn: () => {
      return getThirdLoginSettings();
    },
  });

  // Initialize cache for all plugins
  const initialCache = useMemo<Record<number, FormValues>>(
    () => AUTH_PLUGIN_META.reduce((acc, { id }) => ({ ...acc, [id]: {} }), {}),
    []
  );
  const [cachedValues, setCachedValues] = useState(initialCache);

  const [currentLoginType, setCurrentLoginType] = useState<number>(
    AUTH_PLUGIN_META[0].id
  );
  const prevLoginTypeRef = useRef<number>(1);

  // When data is loaded, initialize the cache
  useEffect(() => {
    if (data && formRef.current?.formApi) {
      const items = omit([], data?.data);
      const filteredItems = filterEditData(items);

      // Initialize cache with API data
      setCachedValues((prev) => ({
        ...prev,
        [filteredItems.loginType || 1]: filteredItems,
      }));

      formRef.current?.formApi.setValues(filteredItems, { isOverride: true });

      // Set initial login type
      setCurrentLoginType(filteredItems.loginType || 1);
      prevLoginTypeRef.current = filteredItems.loginType || 1;
    }
  }, [data, formRef.current?.formApi]);

  const mutation = useMutation({
    mutationFn: setThirdLoginSettings,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      }
    },
  });

  const handleSubmit = () => {
    if (mutation.isLoading) {
      console.debug("mutation.isLoading");
      return;
    }

    formRef.current?.formApi
      .validate()
      .then((values: any) => {
        // Get current form values
        const currentFormValues = formRef.current?.formApi.getValues() || {};

        // Create the updated cache object first
        const updatedCachedValues = {
          ...cachedValues,
          [currentLoginType]: {
            ...cachedValues[currentLoginType],
            ...currentFormValues,
          },
        };

        // Update the state
        setCachedValues(updatedCachedValues);

        // Submit with merged values from the *updated* cache
        const mergedValues = {
          ...AUTH_PLUGIN_META.reduce(
            (acc, { id }) => ({ ...acc, ...updatedCachedValues[id] }),
            {} as FormValues
          ),
          ...currentFormValues,
          loginType: currentLoginType,
        };

        mutation.mutate(mergedValues);
      })
      .catch((err: any) => {
        console.error(err);
        const [formFieldId] = Object.keys(err || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  // 增加动画配置状态
  const [isFormVisible, setIsFormVisible] = useState(false);

  // 插件选择处理函数
  const handlePluginSelect = (pluginId: number) => {
    if (!formRef.current?.formApi) return;

    setIsFormVisible(false);

    // Save current values before switching
    const currentValues = formRef.current.formApi.getValues();
    setCachedValues((prev) => ({
      ...prev,
      [currentLoginType]: { ...prev[currentLoginType], ...currentValues },
    }));

    // Update current login type
    prevLoginTypeRef.current = currentLoginType;
    setCurrentLoginType(pluginId);

    // Switch to new type and restore its cached values
    setTimeout(() => {
      formRef.current.formApi.setValue("loginType", pluginId);

      // Restore cached values for this plugin
      const cachedPluginValues = cachedValues[pluginId] || {};
      Object.entries(cachedPluginValues).forEach(([key, value]) => {
        if (key !== "loginType") {
          // Skip loginType as we already set it
          formRef.current?.formApi.setValue(key, value);
        }
      });

      setIsFormVisible(true);
    }, 300);
  };

  // Add ref for horizontal scrolling
  const authPluginsScrollRef = useRef<HTMLDivElement>(null);

  // Scroll functions for plugin navigation
  const scrollLeft = () => {
    if (authPluginsScrollRef.current) {
      authPluginsScrollRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (authPluginsScrollRef.current) {
      authPluginsScrollRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  return (
    <div className="bg-white shadow-sm p-4 h-fit rounded">
      <div className="mb-6">
        <h2 className="text-xl font-medium mb-2 flex items-center">
          <IconSetting className="mr-2" />
          认证方式配置中心
        </h2>
        <p className="text-gray-500 text-sm">
          选择并配置认证提供商参数，支持多种认证方式
        </p>
      </div>

      <Form<any>
        labelPosition="left"
        labelAlign="right"
        labelWidth={240}
        ref={formRef}
        onSubmit={handleSubmit}
        autoScrollToError
        initValues={{ loginType: 1 }} // 设置初始选中的插件
        onChange={() => setIsFormVisible(true)}
      >
        {({ formState }) => (
          <Spin spinning={isLoading}>
            <div className="mb-6 relative">
              <div className="flex justify-between items-center mb-2">
                <p className="text-gray-700 font-medium">选择认证方式</p>
                <div className="flex gap-2">
                  <Button
                    icon={<IconChevronLeft />}
                    size="small"
                    onClick={scrollLeft}
                    className="flex-shrink-0"
                    type="tertiary"
                  />
                  <Button
                    icon={<IconChevronRight />}
                    size="small"
                    onClick={scrollRight}
                    className="flex-shrink-0"
                    type="tertiary"
                  />
                </div>
              </div>

              {/* Horizontal scrollable auth plugin chips */}
              <div
                className="flex gap-2 overflow-x-auto pb-2 hide-scrollbar"
                ref={authPluginsScrollRef}
                style={{
                  scrollbarWidth: "none",
                  msOverflowStyle: "none",
                }}
              >
                {AUTH_PLUGIN_META.map(({ id, info: plugin }) => (
                  <Tooltip
                    key={id}
                    content={`上次更新: ${plugin.lastUpdate}`}
                    position="top"
                  >
                    <div
                      className={`
                        flex items-center cursor-pointer py-1.5 px-3 rounded-full flex-shrink-0
                        transition-all duration-200 border min-w-[120px]
                        ${
                          formState?.values.loginType === id
                            ? "shadow-sm"
                            : "hover:border-gray-300 bg-white"
                        }
                      `}
                      style={{
                        borderColor:
                          formState?.values.loginType === id
                            ? plugin.color
                            : "#e5e7eb",
                        backgroundColor:
                          formState?.values.loginType === id
                            ? `${plugin.color}10`
                            : undefined,
                      }}
                      onClick={() => handlePluginSelect(id)}
                    >
                      <div
                        className="w-5 h-5 rounded-full mr-2 flex items-center justify-center"
                        style={{
                          backgroundColor: `${plugin.color}20`,
                          color: plugin.color,
                        }}
                      >
                        {plugin.icon}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span
                            className={`text-sm ${formState?.values.loginType === id ? "font-medium" : ""}`}
                          >
                            {plugin.name}
                          </span>

                          {plugin.enabled && (
                            <span
                              className="ml-1.5 w-2 h-2 rounded-full"
                              style={{ backgroundColor: "#52c41a" }}
                            />
                          )}
                        </div>

                        <div className="flex items-center text-xs text-gray-500">
                          <span className="truncate">{plugin.version}</span>
                          {formState?.values.loginType === id && (
                            <span
                              className="ml-1.5 text-xs"
                              style={{ color: plugin.color }}
                            >
                              • 使用中
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </Tooltip>
                ))}

                {/* Hidden input for form value */}
                <Form.Input
                  field="loginType"
                  noLabel={true}
                  className="!hidden"
                />
              </div>

              {/* Hide horizontal scrollbar visually */}
              <style>{`
                .hide-scrollbar::-webkit-scrollbar {
                  display: none;
                }
                .hide-scrollbar {
                  scrollbar-width: none;
                  -ms-overflow-style: none;
                }
              `}</style>
            </div>

            <AnimatePresence mode="wait">
              {isFormVisible && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {AUTH_PLUGIN_META.map(({ id, Form }) =>
                    formState?.values.loginType === id ? (
                      <Form key={id} />
                    ) : null
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex gap-2 justify-end mt-8">
              <button className="btn btn-primary btn-sm rounded w-[150px] transition-all hover:shadow-lg">
                保存配置
              </button>
            </div>
          </Spin>
        )}
      </Form>
    </div>
  );
};
